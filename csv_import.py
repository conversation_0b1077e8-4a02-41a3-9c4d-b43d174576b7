#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV Import Skript für schnitte.csv in die SQLite3 Datenbank.
Importiert Daten in die Tabelle 'ARiL' ohne bestehende Daten zu löschen.
"""

import sqlite3
import csv
import os
import sys
from typing import Optional, Dict, Any

# --- Konfiguration ---
DB_PATH = r"D:\my_ai\4-Lapp\SFM Electron\database\sfm_dashboard.db"
CSV_FILE = "schnitte.csv"
TABLE_NAME = "ARiL"

# --- Hilfsfunktionen ---
def convert_to_int(value: Optional[str]) -> Optional[int]:
    """Konvertiert einen String-Wert in einen Integer."""
    if value is None or not value.strip():
        return None
    try:
        return int(value)
    except (ValueError, TypeError):
        print(f"Warnung: Ungültiger Integer-Wert: {value}")
        return None

def convert_to_float(value: Optional[str]) -> Optional[float]:
    """Konvertiert einen String-Wert in einen Float (deutsche Dezimalzahlen)."""
    if value is None or not value.strip():
        return None
    try:
        return float(value.replace(',', '.'))
    except (ValueError, TypeError):
        print(f"Warnung: Ungültiger Float-Wert: {value}")
        return None

# --- Kernfunktionen ---
def connect_and_prepare_table(db_path: str, table_name: str) -> Optional[sqlite3.Connection]:
    """Stellt die Verbindung zur Datenbank her und erstellt die Tabelle falls sie nicht existiert."""
    if not os.path.exists(os.path.dirname(db_path)):
        print(f"✗ Fehler: Das Verzeichnis für die Datenbank '{os.path.dirname(db_path)}' existiert nicht.")
        return None

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Prüfen ob Tabelle bereits existiert
        cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
        table_exists = cursor.fetchone() is not None

        if table_exists:
            print(f"✓ Tabelle '{table_name}' existiert bereits - Daten werden hinzugefügt.")
        else:
            # Tabelle erstellen für Schnittdaten - dynamische Spalten basierend auf CSV Header
            # Erstelle zunächst eine Basis-Tabelle
            cursor.execute(f'''
                CREATE TABLE {table_name} (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Datum TEXT UNIQUE,
                    import_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            print(f"✓ Basis-Tabelle '{table_name}' erstellt.")

        conn.commit()
        return conn
    except sqlite3.Error as e:
        print(f"✗ Datenbankfehler: {e}")
        return None

def add_columns_to_table(conn: sqlite3.Connection, table_name: str, columns: list):
    """Fügt Spalten zur Tabelle hinzu, falls sie noch nicht existieren."""
    cursor = conn.cursor()

    # Bestehende Spalten abrufen
    cursor.execute(f"PRAGMA table_info({table_name})")
    existing_columns = [row[1] for row in cursor.fetchall()]

    for column in columns:
        if column not in existing_columns and column != 'Datum':  # Datum ist bereits vorhanden
            try:
                cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN `{column}` INTEGER")
                print(f"✓ Spalte '{column}' hinzugefügt.")
            except sqlite3.Error as e:
                print(f"✗ Fehler beim Hinzufügen der Spalte '{column}': {e}")

    conn.commit()

def import_csv_data(conn: sqlite3.Connection, table_name: str, csv_file_path: str):
    """Importiert die Daten aus der schnitte.csv."""
    cursor = conn.cursor()
    import_count = 0
    error_count = 0

    with open(csv_file_path, 'r', encoding='utf-8-sig') as csvfile:
        reader = csv.DictReader(csvfile, delimiter=';')

        # Spalten aus CSV Header extrahieren (außer Datum)
        csv_columns = [col for col in reader.fieldnames if col != 'Datum']

        # Spalten zur Tabelle hinzufügen falls nötig
        add_columns_to_table(conn, table_name, csv_columns)

        for row_num, row in enumerate(reader, start=2):
            try:
                datum = row.get('Datum')
                if not datum or not datum.strip():
                    continue  # Leere Zeilen überspringen

                # Prüfen ob Datum bereits existiert
                cursor.execute(f"SELECT id FROM {table_name} WHERE Datum = ?", (datum,))
                existing_row = cursor.fetchone()

                if existing_row:
                    # Update bestehende Zeile
                    update_parts = []
                    values = []
                    for col in csv_columns:
                        value = convert_to_int(row.get(col))
                        if value is not None:
                            update_parts.append(f"`{col}` = ?")
                            values.append(value)

                    if update_parts:
                        values.append(datum)
                        sql = f"UPDATE {table_name} SET {', '.join(update_parts)} WHERE Datum = ?"
                        cursor.execute(sql, values)
                        print(f"✓ Zeile für Datum {datum} aktualisiert.")
                else:
                    # Neue Zeile einfügen
                    columns = ['Datum'] + csv_columns
                    values = [datum]
                    placeholders = ['?']

                    for col in csv_columns:
                        value = convert_to_int(row.get(col))
                        values.append(value)
                        placeholders.append('?')

                    quoted_columns = ', '.join([f'`{col}`' for col in columns])
                    sql = f"INSERT INTO {table_name} ({quoted_columns}) VALUES ({', '.join(placeholders)})"
                    cursor.execute(sql, values)

                import_count += 1

            except Exception as e:
                error_count += 1
                print(f"✗ Fehler in Zeile {row_num}: {e} (Daten: {row})")

    conn.commit()
    print(f"\n✓ Import abgeschlossen: {import_count} Zeilen importiert, {error_count} Fehler.")

def main():
    """Hauptfunktion des Skripts."""
    print(f"🚀 Import für '{CSV_FILE}' wird gestartet...")
    print(f"🗄️  Zieldatenbank: {DB_PATH}")
    print(f"→ Zieltabelle: {TABLE_NAME}")
    print("📝 Bestehende Daten werden NICHT gelöscht - neue Daten werden hinzugefügt/aktualisiert")

    if not os.path.exists(CSV_FILE):
        print(f"✗ Fehler: Die Datei '{CSV_FILE}' wurde nicht gefunden.")
        sys.exit(1)

    conn = connect_and_prepare_table(DB_PATH, TABLE_NAME)
    if not conn:
        print("✗ Datenbank-Setup fehlgeschlagen.")
        sys.exit(1)

    try:
        import_csv_data(conn, TABLE_NAME, CSV_FILE)
        print(f"\n✅ Import für '{CSV_FILE}' erfolgreich abgeschlossen!")
        print("📊 Die Daten wurden zur bestehenden Tabelle hinzugefügt.")
    finally:
        conn.close()
        print("\n🔐 Datenbankverbindung geschlossen.")

if __name__ == "__main__":
    main()
