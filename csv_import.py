#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV Import Skript für maschinen.csv in die SQLite3 Datenbank.
Erstellt/Aktualisiert die Tabelle 'maschinen'.
"""

import sqlite3
import csv
import os
import sys
from typing import Optional

# --- Konfiguration ---
DB_PATH = r"D:\my_ai\4-Lapp\SFM Electron\database\sfm_dashboard.db"
CSV_FILE = "maschinen.csv"
TABLE_NAME = "maschinen"

# --- Hilfsfunktionen ---
def convert_schnitte_pro_std(value: Optional[str]) -> Optional[float]:
    """Konvertiert den Wert für schnitteProStd in einen Float.
       Behandelt Bereiche (z.B. '7 - 8') und deutsche Dezimalzahlen.
    """
    if value is None or not value.strip():
        return None
    
    # Nimm den Durchschnitt, falls ein Bereich angegeben ist (z.B. "7 - 8")
    if '-' in value:
        try:
            low, high = map(str.strip, value.split('-'))
            return (float(low.replace(',', '.')) + float(high.replace(',', '.'))) / 2
        except ValueError:
            print(f"Warnung: Ungültiger Bereich im Wert: {value}")
            return None
            
    # Normaler Dezimalwert
    try:
        return float(value.replace(',', '.'))
    except (ValueError, TypeError):
        print(f"Warnung: Ungültiger Dezimalwert: {value}")
        return None

# --- Kernfunktionen ---
def connect_and_prepare_table(db_path: str, table_name: str) -> Optional[sqlite3.Connection]:
    """Stellt die Verbindung zur Datenbank her und bereitet die Tabelle vor."""
    if not os.path.exists(os.path.dirname(db_path)):
        print(f"✗ Fehler: Das Verzeichnis für die Datenbank '{os.path.dirname(db_path)}' existiert nicht.")
        return None

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # Tabelle immer löschen und neu erstellen, um Konsistenz zu garantieren
        cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
        print(f"✓ Alte Tabelle '{table_name}' (falls vorhanden) gelöscht.")

        # Tabelle neu erstellen
        cursor.execute(f'''
            CREATE TABLE {table_name} (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                Machine TEXT UNIQUE,
                schnitteProStd REAL,
                import_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print(f"✓ Tabelle '{table_name}' neu erstellt.")

        conn.commit()
        return conn
    except sqlite3.Error as e:
        print(f"✗ Datenbankfehler: {e}")
        return None

def import_csv_data(conn: sqlite3.Connection, table_name: str, csv_file_path: str):
    """Importiert die Daten aus der maschinen.csv."""
    cursor = conn.cursor()
    import_count = 0
    error_count = 0

    with open(csv_file_path, 'r', encoding='utf-8-sig') as csvfile:
        reader = csv.DictReader(csvfile, delimiter=';')
        
        for row_num, row in enumerate(reader, start=2):
            try:
                machine = row.get('Machine')
                schnitte = convert_schnitte_pro_std(row.get('schnitteProStd'))

                if machine and schnitte is not None:
                    # ON CONFLICT(Machine) DO UPDATE sorgt dafür, dass bestehende Einträge aktualisiert werden.
                    sql = f"""INSERT INTO {table_name} (Machine, schnitteProStd) 
                             VALUES (?, ?) """
                    cursor.execute(sql, (machine, schnitte))
                    import_count += 1
                else:
                    raise ValueError("Fehlende oder ungültige Daten")

            except Exception as e:
                error_count += 1
                print(f"✗ Fehler in Zeile {row_num}: {e} (Daten: {row})")
    
    conn.commit()
    print(f"\n✓ Import abgeschlossen: {import_count} Zeilen importiert, {error_count} Fehler.")

def main():
    """Hauptfunktion des Skripts."""
    print(f"🚀 Import für '{CSV_FILE}' wird gestartet...")
    print(f"🗄️  Zieldatenbank: {DB_PATH}")
    print(f"→ Zieltabelle: {TABLE_NAME}")

    if not os.path.exists(CSV_FILE):
        print(f"✗ Fehler: Die Datei '{CSV_FILE}' wurde nicht gefunden.")
        sys.exit(1)

    conn = connect_and_prepare_table(DB_PATH, TABLE_NAME)
    if not conn:
        print("✗ Datenbank-Setup fehlgeschlagen.")
        sys.exit(1)
    
    try:
        import_csv_data(conn, TABLE_NAME, CSV_FILE)
        print(f"\n✅ Import für '{CSV_FILE}' erfolgreich abgeschlossen!")
    finally:
        conn.close()
        print("\n🔐 Datenbankverbindung geschlossen.")

if __name__ == "__main__":
    main()
